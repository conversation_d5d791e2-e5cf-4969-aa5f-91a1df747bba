<?php
/**
 * Simple Microsoft Office 365 SMTP Test Script
 * Tests basic connectivity and configuration
 */

// Email configuration
$smtp_host = 'smtp.office365.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'E$@24680';

echo "=== Microsoft Office 365 SMTP Configuration Test ===\n\n";
echo "Configuration:\n";
echo "SMTP Host: $smtp_host\n";
echo "SMTP Port: $smtp_port\n";
echo "Username: $smtp_username\n\n";

// Test 1: Basic TCP connection
echo "Test 1: Testing TCP connection to SMTP server...\n";
$connection = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($connection) {
    echo "✓ Successfully connected to $smtp_host:$smtp_port\n";
    
    // Read server greeting
    $response = fgets($connection, 515);
    echo "Server greeting: " . trim($response) . "\n";
    
    fclose($connection);
} else {
    echo "✗ Failed to connect to $smtp_host:$smtp_port\n";
    echo "Error: $errstr ($errno)\n";
}
echo "\n";

// Test 2: SMTP conversation test
echo "Test 2: Testing SMTP conversation...\n";
$socket = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($socket) {
    // Read greeting
    $response = fgets($socket, 515);
    echo "Server: " . trim($response) . "\n";
    
    // Send EHLO
    fputs($socket, "EHLO localhost\r\n");
    $response = '';
    while ($line = fgets($socket, 515)) {
        $response .= $line;
        if (substr($line, 3, 1) == ' ') break;
    }
    echo "EHLO response: " . trim($response) . "\n";
    
    // Check for STARTTLS support
    if (strpos($response, 'STARTTLS') !== false) {
        echo "✓ STARTTLS is supported\n";
        
        // Test STARTTLS
        fputs($socket, "STARTTLS\r\n");
        $response = fgets($socket, 515);
        echo "STARTTLS response: " . trim($response) . "\n";
        
        if (strpos($response, '220') === 0) {
            echo "✓ TLS encryption is ready\n";
        } else {
            echo "✗ TLS encryption failed\n";
        }
    } else {
        echo "✗ STARTTLS not supported\n";
    }
    
    // Send QUIT
    fputs($socket, "QUIT\r\n");
    $response = fgets($socket, 515);
    echo "QUIT response: " . trim($response) . "\n";
    
    fclose($socket);
} else {
    echo "✗ Could not establish socket connection\n";
    echo "Error: $errstr ($errno)\n";
}
echo "\n";

// Test 3: DNS resolution
echo "Test 3: Testing DNS resolution...\n";
$ip = gethostbyname($smtp_host);
if ($ip != $smtp_host) {
    echo "✓ DNS resolution successful: $smtp_host -> $ip\n";
} else {
    echo "✗ DNS resolution failed for $smtp_host\n";
}
echo "\n";

// Test 4: Check PHP extensions
echo "Test 4: Checking PHP extensions...\n";
$required_extensions = ['openssl', 'sockets'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ $ext extension is loaded\n";
    } else {
        echo "✗ $ext extension is NOT loaded\n";
    }
}
echo "\n";

// Test 5: Configuration recommendations
echo "=== Configuration Summary ===\n";
echo "Your Microsoft Office 365 SMTP settings:\n";
echo "- Host: $smtp_host\n";
echo "- Port: $smtp_port\n";
echo "- Security: STARTTLS (TLS)\n";
echo "- Authentication: Yes\n";
echo "- Username: $smtp_username\n";
echo "- Password: [CONFIGURED]\n\n";

echo "=== WordPress Integration ===\n";
echo "To use these settings in WordPress, add this to your wp-config.php:\n\n";
echo "define('SMTP_HOST', '$smtp_host');\n";
echo "define('SMTP_PORT', $smtp_port);\n";
echo "define('SMTP_USER', '$smtp_username');\n";
echo "define('SMTP_PASS', '$smtp_password');\n";
echo "define('SMTP_SECURE', 'tls');\n";
echo "define('SMTP_FROM', '$smtp_username');\n";
echo "define('SMTP_FROM_NAME', 'Al Takaful Insurance Services');\n\n";

echo "Or use a WordPress SMTP plugin like:\n";
echo "- WP Mail SMTP\n";
echo "- Easy WP SMTP\n";
echo "- Post SMTP\n\n";

echo "=== Security Notes ===\n";
echo "- Consider using App Passwords instead of the main account password\n";
echo "- Store credentials securely (environment variables or WordPress constants)\n";
echo "- Enable two-factor authentication on the email account\n";
echo "- Monitor email sending logs\n\n";

echo "=== Test Complete ===\n";
echo "If all tests passed, your SMTP configuration should work correctly.\n";
?>
