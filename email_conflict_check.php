<?php
/**
 * WordPress Email Conflict Diagnostic
 * Checks for conflicts between email plugins and configurations
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Conflict Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; margin: 10px 0; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; margin: 10px 0; }
        .warning { color: orange; background: #fff8f0; padding: 10px; border: 1px solid orange; margin: 10px 0; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; margin: 10px 0; }
        pre { background: #f8f8f8; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>WordPress Email Conflict Diagnostic</h1>
    
    <?php
    echo "<div class='section'>";
    echo "<h2>1. Active Email-Related Plugins</h2>";
    
    $active_plugins = get_option('active_plugins');
    $email_plugins = [];
    
    foreach ($active_plugins as $plugin) {
        $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin);
        $plugin_name = $plugin_data['Name'];
        
        if (stripos($plugin, 'smtp') !== false || 
            stripos($plugin, 'mail') !== false || 
            stripos($plugin_name, 'smtp') !== false || 
            stripos($plugin_name, 'mail') !== false) {
            $email_plugins[] = [
                'file' => $plugin,
                'name' => $plugin_name,
                'version' => $plugin_data['Version']
            ];
        }
    }
    
    if (!empty($email_plugins)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Plugin Name</th><th>File</th><th>Version</th></tr>";
        foreach ($email_plugins as $plugin) {
            echo "<tr>";
            echo "<td>" . $plugin['name'] . "</td>";
            echo "<td>" . $plugin['file'] . "</td>";
            echo "<td>" . $plugin['version'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (count($email_plugins) > 1) {
            echo "<div class='warning'>⚠ Multiple email plugins detected - this can cause conflicts!</div>";
        }
    } else {
        echo "<div class='info'>No email-specific plugins detected</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>2. Fluent SMTP Configuration</h2>";
    
    $fluent_settings = get_option('fluentmail-settings');
    if ($fluent_settings) {
        echo "<div class='success'>✓ Fluent SMTP settings found</div>";
        echo "<pre>" . print_r($fluent_settings, true) . "</pre>";
        
        // Check if Fluent SMTP is properly configured
        if (isset($fluent_settings['connections']['smtp']['host'])) {
            $host = $fluent_settings['connections']['smtp']['host'];
            $port = $fluent_settings['connections']['smtp']['port'];
            $username = $fluent_settings['connections']['smtp']['username'];
            
            echo "<p><strong>Current Fluent SMTP Config:</strong></p>";
            echo "<ul>";
            echo "<li>Host: " . $host . "</li>";
            echo "<li>Port: " . $port . "</li>";
            echo "<li>Username: " . $username . "</li>";
            echo "</ul>";
            
            if ($host === 'smtp.office365.com' && $port == 587) {
                echo "<div class='success'>✓ Fluent SMTP is configured for Office 365</div>";
            } else {
                echo "<div class='error'>✗ Fluent SMTP is NOT configured for Office 365</div>";
            }
        }
    } else {
        echo "<div class='error'>✗ Fluent SMTP settings not found</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>3. WordPress Hooks Check</h2>";
    
    // Check what functions are hooked to phpmailer_init
    global $wp_filter;
    if (isset($wp_filter['phpmailer_init'])) {
        echo "<p><strong>Functions hooked to 'phpmailer_init':</strong></p>";
        echo "<ul>";
        foreach ($wp_filter['phpmailer_init']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                $function_name = 'Unknown';
                if (is_array($callback['function'])) {
                    if (is_object($callback['function'][0])) {
                        $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                    } else {
                        $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                    }
                } else {
                    $function_name = $callback['function'];
                }
                echo "<li>Priority $priority: $function_name</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<div class='warning'>No functions hooked to phpmailer_init</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>4. Test wp_mail Function</h2>";
    
    if (isset($_POST['test_wp_mail'])) {
        $test_email = sanitize_email($_POST['test_email']);
        
        // Capture PHPMailer debug output
        $debug_output = '';
        add_action('phpmailer_init', function($phpmailer) use (&$debug_output) {
            $phpmailer->SMTPDebug = 3;
            $phpmailer->Debugoutput = function($str, $level) use (&$debug_output) {
                $debug_output .= "Level $level: " . $str . "\n";
            };
        }, 999); // High priority to run last
        
        // Capture mail errors
        $mail_error = '';
        add_action('wp_mail_failed', function($wp_error) use (&$mail_error) {
            $mail_error = $wp_error->get_error_message();
        });
        
        $subject = 'WordPress Mail Test - ' . date('Y-m-d H:i:s');
        $message = 'This is a test email to diagnose email sending issues.';
        $headers = array('Content-Type: text/html; charset=UTF-8');
        
        $result = wp_mail($test_email, $subject, $message, $headers);
        
        if ($result) {
            echo "<div class='success'>✓ wp_mail() returned true</div>";
        } else {
            echo "<div class='error'>✗ wp_mail() returned false</div>";
            if ($mail_error) {
                echo "<div class='error'>Error: " . $mail_error . "</div>";
            }
        }
        
        if ($debug_output) {
            echo "<h3>PHPMailer Debug Output:</h3>";
            echo "<pre>" . htmlspecialchars($debug_output) . "</pre>";
        }
    }
    ?>
    
    <form method="post">
        <p>
            <label>Test Email Address:</label><br>
            <input type="email" name="test_email" value="<EMAIL>" required style="width: 300px;">
        </p>
        <p>
            <input type="submit" name="test_wp_mail" value="Test wp_mail() with Debug" style="padding: 10px 20px; background: #0073aa; color: white; border: none;">
        </p>
    </form>
    </div>

    <?php
    echo "<div class='section'>";
    echo "<h2>5. Forminator Email Settings</h2>";
    
    // Check Forminator settings
    $forminator_settings = get_option('forminator_settings');
    if ($forminator_settings) {
        echo "<div class='success'>✓ Forminator settings found</div>";
        echo "<pre>" . print_r($forminator_settings, true) . "</pre>";
    } else {
        echo "<div class='warning'>Forminator settings not found</div>";
    }
    
    // Check for Forminator forms
    $forms = get_posts(array(
        'post_type' => 'forminator_forms',
        'numberposts' => -1
    ));
    
    if (!empty($forms)) {
        echo "<p><strong>Forminator Forms:</strong></p>";
        echo "<ul>";
        foreach ($forms as $form) {
            echo "<li>" . $form->post_title . " (ID: " . $form->ID . ")</li>";
        }
        echo "</ul>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>6. Recommendations</h2>";
    echo "<div class='info'>";
    echo "<h3>To Fix the Issue:</h3>";
    echo "<ol>";
    echo "<li><strong>Test Fluent SMTP directly</strong> using its built-in test feature</li>";
    echo "<li><strong>If Fluent SMTP test works:</strong> The issue is with Forminator configuration</li>";
    echo "<li><strong>If Fluent SMTP test fails:</strong> Check the Fluent SMTP logs for detailed errors</li>";
    echo "<li><strong>Check plugin priority:</strong> Ensure Fluent SMTP loads before other email plugins</li>";
    echo "<li><strong>Temporarily deactivate other email plugins</strong> to test for conflicts</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
    ?>

    <div class="section">
        <h2>7. Quick Actions</h2>
        <p><strong>Try these in order:</strong></p>
        <ol>
            <li>Go to <strong>Fluent SMTP → Settings</strong> and click <strong>"Send Test Email"</strong></li>
            <li>If that works, go to <strong>Forminator → Settings → Email</strong> and check sender settings</li>
            <li>If still failing, temporarily deactivate Fluent SMTP and test with the functions.php code</li>
        </ol>
    </div>
</body>
</html>
