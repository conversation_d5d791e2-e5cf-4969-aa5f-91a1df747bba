<?php
/**
 * Microsoft Office 365 SMTP Test Script
 * Tests the provided email configuration
 */

// Email configuration
$smtp_host = 'smtp.office365.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'E$@24680';
$from_email = '<EMAIL>';
$from_name = 'Al Takaful Insurance Services';

// Test recipient (change this to your test email)
$to_email = '<EMAIL>'; // Change this to a valid test email
$to_name = 'Test Recipient';

// Email content
$subject = 'SMTP Configuration Test - ' . date('Y-m-d H:i:s');
$body = '
<html>
<head>
    <title>SMTP Test Email</title>
</head>
<body>
    <h2>SMTP Configuration Test</h2>
    <p>This is a test email to verify the Microsoft Office 365 SMTP configuration.</p>
    <p><strong>Configuration Details:</strong></p>
    <ul>
        <li>SMTP Host: ' . $smtp_host . '</li>
        <li>SMTP Port: ' . $smtp_port . '</li>
        <li>Username: ' . $smtp_username . '</li>
        <li>From Email: ' . $from_email . '</li>
    </ul>
    <p>Sent at: ' . date('Y-m-d H:i:s') . '</p>
    <p>If you receive this email, the SMTP configuration is working correctly!</p>
</body>
</html>
';

echo "=== Microsoft Office 365 SMTP Configuration Test ===\n\n";
echo "Configuration:\n";
echo "SMTP Host: $smtp_host\n";
echo "SMTP Port: $smtp_port\n";
echo "Username: $smtp_username\n";
echo "From Email: $from_email\n\n";

// Test 1: Basic connection test
echo "Test 1: Testing SMTP connection...\n";
$connection = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($connection) {
    echo "✓ Successfully connected to $smtp_host:$smtp_port\n";
    fclose($connection);
} else {
    echo "✗ Failed to connect to $smtp_host:$smtp_port\n";
    echo "Error: $errstr ($errno)\n";
}
echo "\n";

// Test 2: Using PHPMailer (if available)
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "Test 2: Testing with PHPMailer...\n";
    
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        $mail->SMTPSecure = 'tls';
        $mail->Port = $smtp_port;
        $mail->SMTPDebug = 0; // Set to 2 for detailed debug output
        
        // Recipients
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($to_email, $to_name);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->AltBody = strip_tags($body);
        
        // Test connection only (don't send)
        $mail->SMTPDebug = 0;
        if ($mail->smtpConnect()) {
            echo "✓ PHPMailer SMTP authentication successful\n";
            $mail->smtpClose();
            
            // Uncomment the line below to actually send the test email
            // $mail->send();
            // echo "✓ Test email sent successfully!\n";
            
        } else {
            echo "✗ PHPMailer SMTP authentication failed\n";
        }
        
    } catch (Exception $e) {
        echo "✗ PHPMailer Error: {$mail->ErrorInfo}\n";
    }
} else {
    echo "Test 2: PHPMailer not available, using native PHP mail functions...\n";
    
    // Test 3: Using native PHP with stream context
    $headers = [
        'From' => "$from_name <$from_email>",
        'Reply-To' => $from_email,
        'Content-Type' => 'text/html; charset=UTF-8',
        'MIME-Version' => '1.0'
    ];
    
    $context = stream_context_create([
        'smtp' => [
            'host' => $smtp_host,
            'port' => $smtp_port,
            'username' => $smtp_username,
            'password' => $smtp_password,
            'encryption' => 'tls'
        ]
    ]);
    
    echo "Note: Native PHP mail() function configured for SMTP\n";
    echo "To actually send email, uncomment the mail() function call below\n";
    
    // Uncomment to actually send email
    // $result = mail($to_email, $subject, $body, implode("\r\n", $headers));
    // echo $result ? "✓ Email sent successfully!\n" : "✗ Email sending failed\n";
}

echo "\n";

// Test 4: Manual SMTP conversation
echo "Test 3: Manual SMTP conversation test...\n";
$socket = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($socket) {
    $response = fgets($socket, 515);
    echo "Server response: " . trim($response) . "\n";
    
    // Send EHLO command
    fputs($socket, "EHLO localhost\r\n");
    $response = fgets($socket, 515);
    echo "EHLO response: " . trim($response) . "\n";
    
    // Start TLS
    fputs($socket, "STARTTLS\r\n");
    $response = fgets($socket, 515);
    echo "STARTTLS response: " . trim($response) . "\n";
    
    if (strpos($response, '220') === 0) {
        echo "✓ TLS encryption is supported\n";
    } else {
        echo "✗ TLS encryption may not be supported\n";
    }
    
    fclose($socket);
} else {
    echo "✗ Could not establish socket connection\n";
}

echo "\n=== Test Complete ===\n";
echo "\nNOTE: To actually send test emails, you need to:\n";
echo "1. Change the \$to_email variable to a valid test email address\n";
echo "2. Uncomment the mail sending lines in the script\n";
echo "3. Make sure PHPMailer is installed if you want to use it\n";
echo "\nFor WordPress integration, these settings should work with:\n";
echo "- WP Mail SMTP plugin\n";
echo "- Easy WP SMTP plugin\n";
echo "- Native wp_mail() function with proper configuration\n";
?>
