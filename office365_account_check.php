<?php
/**
 * Office 365 Account Status Check
 * Tests various authentication scenarios
 */

$smtp_host = 'smtp.office365.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'E$@24680';

echo "=== Office 365 Account Status Check ===\n\n";

// Test 1: Basic connection and server capabilities
echo "Test 1: Checking server capabilities...\n";
$socket = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($socket) {
    // Read greeting
    $response = fgets($socket, 515);
    echo "Server greeting: " . trim($response) . "\n";
    
    // Send EHLO
    fputs($socket, "EHLO localhost\r\n");
    $response = '';
    while ($line = fgets($socket, 515)) {
        $response .= $line;
        if (substr($line, 3, 1) == ' ') break;
    }
    
    echo "Server capabilities:\n";
    $lines = explode("\n", $response);
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line)) {
            echo "  " . $line . "\n";
        }
    }
    
    fclose($socket);
} else {
    echo "✗ Cannot connect to server: $errstr ($errno)\n";
}

echo "\n";

// Test 2: Detailed authentication attempt
echo "Test 2: Detailed authentication test...\n";
$socket = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 10);
if ($socket) {
    // Read greeting
    $response = fgets($socket, 515);
    
    // Send EHLO
    fputs($socket, "EHLO localhost\r\n");
    $response = '';
    while ($line = fgets($socket, 515)) {
        $response .= $line;
        if (substr($line, 3, 1) == ' ') break;
    }
    
    // Start TLS
    fputs($socket, "STARTTLS\r\n");
    $response = fgets($socket, 515);
    echo "STARTTLS response: " . trim($response) . "\n";
    
    if (strpos($response, '220') === 0) {
        // Enable crypto
        if (stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
            echo "✓ TLS encryption successful\n";
            
            // Send EHLO again after TLS
            fputs($socket, "EHLO localhost\r\n");
            $response = '';
            while ($line = fgets($socket, 515)) {
                $response .= $line;
                if (substr($line, 3, 1) == ' ') break;
            }
            
            // Try AUTH LOGIN
            fputs($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket, 515);
            echo "AUTH LOGIN response: " . trim($response) . "\n";
            
            if (strpos($response, '334') === 0) {
                // Send username
                fputs($socket, base64_encode($smtp_username) . "\r\n");
                $response = fgets($socket, 515);
                echo "Username response: " . trim($response) . "\n";
                
                if (strpos($response, '334') === 0) {
                    // Send password
                    fputs($socket, base64_encode($smtp_password) . "\r\n");
                    $response = fgets($socket, 515);
                    echo "Password response: " . trim($response) . "\n";
                    
                    if (strpos($response, '235') === 0) {
                        echo "✓ Authentication successful!\n";
                    } else {
                        echo "✗ Authentication failed!\n";
                        
                        // Detailed error analysis
                        if (strpos($response, '535') !== false) {
                            echo "\n🔍 Error Analysis:\n";
                            echo "Error 535 typically means:\n";
                            echo "- Account has 2FA enabled and requires App Password\n";
                            echo "- Security defaults are enabled in Azure AD\n";
                            echo "- Legacy authentication is disabled\n";
                            echo "- Account is locked or suspended\n";
                        }
                    }
                } else {
                    echo "✗ Username rejected: " . trim($response) . "\n";
                }
            } else {
                echo "✗ AUTH LOGIN not supported: " . trim($response) . "\n";
            }
        } else {
            echo "✗ TLS encryption failed\n";
        }
    } else {
        echo "✗ STARTTLS failed: " . trim($response) . "\n";
    }
    
    fclose($socket);
}

echo "\n=== Diagnosis ===\n";
echo "Based on the test results above:\n\n";

echo "If you see 'Authentication successful' - the credentials work fine.\n";
echo "If you see Error 535 - this indicates Office 365 security restrictions.\n\n";

echo "=== Solutions ===\n";
echo "1. **Generate App Password (Recommended):**\n";
echo "   - Log into Microsoft 365 admin center\n";
echo "   - Go to Security → Multi-factor authentication\n";
echo "   - Generate App Password for SMTP\n";
echo "   - Use App Password instead of regular password\n\n";

echo "2. **Check Security Defaults:**\n";
echo "   - Azure AD admin center → Properties → Security defaults\n";
echo "   - If enabled, you MUST use App Passwords\n\n";

echo "3. **Enable Legacy Authentication (Less Secure):**\n";
echo "   - Microsoft 365 admin → Settings → Org settings\n";
echo "   - Security & privacy → Modern authentication\n";
echo "   - Allow legacy authentication for SMTP\n\n";

echo "4. **Check Account Status:**\n";
echo "   - Verify account is not locked\n";
echo "   - Check if account has proper licenses\n";
echo "   - Ensure SMTP is allowed for the account\n\n";

echo "=== Next Steps ===\n";
echo "1. Try generating an App Password first\n";
echo "2. Replace 'E\$@24680' with the App Password in Fluent SMTP\n";
echo "3. Test again\n";
?>
