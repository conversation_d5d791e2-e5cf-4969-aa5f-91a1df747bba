<?php
/**
 * WordPress Email Test Script for Microsoft Office 365
 * Place this file in your WordPress root directory and access via browser
 */

// Prevent direct access if not in WordPress environment
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    require_once(dirname(__FILE__) . '/wp-config.php');
}

// Email configuration for Office 365
$smtp_settings = [
    'host' => 'smtp.office365.com',
    'port' => 587,
    'username' => '<EMAIL>',
    'password' => 'E$@24680',
    'encryption' => 'tls',
    'from_email' => '<EMAIL>',
    'from_name' => 'Al Takaful Insurance Services'
];

// Test email settings
$test_email = '<EMAIL>'; // Change this to your test email
$subject = 'WordPress SMTP Test - ' . date('Y-m-d H:i:s');
$message = '
<h2>WordPress Email Test</h2>
<p>This is a test email from your WordPress site using Microsoft Office 365 SMTP.</p>
<p><strong>Configuration:</strong></p>
<ul>
    <li>SMTP Host: ' . $smtp_settings['host'] . '</li>
    <li>Port: ' . $smtp_settings['port'] . '</li>
    <li>Username: ' . $smtp_settings['username'] . '</li>
    <li>Encryption: ' . strtoupper($smtp_settings['encryption']) . '</li>
</ul>
<p>Sent at: ' . date('Y-m-d H:i:s') . '</p>
<p>If you receive this email, your SMTP configuration is working correctly!</p>
';

?>
<!DOCTYPE html>
<html>
<head>
    <title>WordPress Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; }
        .config { background: #f5f5f5; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f8f8f8; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>WordPress Email Configuration Test</h1>
    
    <div class="config">
        <h3>Current SMTP Configuration:</h3>
        <ul>
            <li><strong>SMTP Host:</strong> <?php echo $smtp_settings['host']; ?></li>
            <li><strong>Port:</strong> <?php echo $smtp_settings['port']; ?></li>
            <li><strong>Username:</strong> <?php echo $smtp_settings['username']; ?></li>
            <li><strong>Encryption:</strong> <?php echo strtoupper($smtp_settings['encryption']); ?></li>
            <li><strong>From Email:</strong> <?php echo $smtp_settings['from_email']; ?></li>
            <li><strong>From Name:</strong> <?php echo $smtp_settings['from_name']; ?></li>
        </ul>
    </div>

    <?php
    if (isset($_POST['test_email'])) {
        echo "<h3>Test Results:</h3>";
        
        // Configure WordPress to use SMTP
        add_action('phpmailer_init', function($phpmailer) use ($smtp_settings) {
            $phpmailer->isSMTP();
            $phpmailer->Host = $smtp_settings['host'];
            $phpmailer->SMTPAuth = true;
            $phpmailer->Username = $smtp_settings['username'];
            $phpmailer->Password = $smtp_settings['password'];
            $phpmailer->SMTPSecure = $smtp_settings['encryption'];
            $phpmailer->Port = $smtp_settings['port'];
            $phpmailer->setFrom($smtp_settings['from_email'], $smtp_settings['from_name']);
        });
        
        // Send test email
        $headers = array('Content-Type: text/html; charset=UTF-8');
        $result = wp_mail($test_email, $subject, $message, $headers);
        
        if ($result) {
            echo '<div class="success">✓ Test email sent successfully to ' . $test_email . '</div>';
        } else {
            echo '<div class="error">✗ Failed to send test email</div>';
            
            // Get the last error
            global $phpmailer;
            if (isset($phpmailer) && !empty($phpmailer->ErrorInfo)) {
                echo '<div class="error">Error details: ' . $phpmailer->ErrorInfo . '</div>';
            }
        }
    }
    
    // Test SMTP connection
    echo "<h3>Connection Test:</h3>";
    $connection = @fsockopen($smtp_settings['host'], $smtp_settings['port'], $errno, $errstr, 10);
    if ($connection) {
        echo '<div class="success">✓ Successfully connected to ' . $smtp_settings['host'] . ':' . $smtp_settings['port'] . '</div>';
        fclose($connection);
    } else {
        echo '<div class="error">✗ Failed to connect to ' . $smtp_settings['host'] . ':' . $smtp_settings['port'] . '<br>Error: ' . $errstr . ' (' . $errno . ')</div>';
    }
    ?>

    <h3>Send Test Email:</h3>
    <form method="post">
        <p>
            <label for="email">Test Email Address:</label><br>
            <input type="email" name="test_email_address" value="<?php echo $test_email; ?>" style="width: 300px;" required>
        </p>
        <p>
            <input type="submit" name="test_email" value="Send Test Email" style="padding: 10px 20px; background: #0073aa; color: white; border: none; cursor: pointer;">
        </p>
    </form>

    <h3>WordPress Plugin Configuration:</h3>
    <div class="info">
        <p>To configure these settings in WordPress, you can use one of these plugins:</p>
        <ul>
            <li><strong>WP Mail SMTP</strong> - Most popular SMTP plugin</li>
            <li><strong>Easy WP SMTP</strong> - Simple configuration</li>
            <li><strong>Post SMTP</strong> - Advanced features</li>
        </ul>
        
        <p>Or add this code to your theme's functions.php:</p>
        <pre><?php echo htmlspecialchars("
add_action('phpmailer_init', 'configure_smtp');
function configure_smtp(\$phpmailer) {
    \$phpmailer->isSMTP();
    \$phpmailer->Host = 'smtp.office365.com';
    \$phpmailer->SMTPAuth = true;
    \$phpmailer->Username = '<EMAIL>';
    \$phpmailer->Password = 'E\$@24680';
    \$phpmailer->SMTPSecure = 'tls';
    \$phpmailer->Port = 587;
    \$phpmailer->setFrom('<EMAIL>', 'Al Takaful Insurance Services');
}"); ?></pre>
    </div>

    <h3>Security Notes:</h3>
    <div class="error">
        <ul>
            <li>Never store passwords in plain text in production</li>
            <li>Use environment variables or WordPress constants for sensitive data</li>
            <li>Consider using App Passwords instead of the main account password</li>
            <li>Remove this test file after testing</li>
        </ul>
    </div>
</body>
</html>
