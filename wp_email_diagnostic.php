<?php
/**
 * WordPress Email Diagnostic Script
 * Diagnoses email sending issues in WordPress
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

// Force WordPress to load
if (!function_exists('wp_mail')) {
    require_once(ABSPATH . 'wp-includes/pluggable.php');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WordPress Email Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; margin: 10px 0; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; margin: 10px 0; }
        .warning { color: orange; background: #fff8f0; padding: 10px; border: 1px solid orange; margin: 10px 0; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; margin: 10px 0; }
        pre { background: #f8f8f8; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>WordPress Email Diagnostic Report</h1>
    
    <?php
    echo "<div class='section'>";
    echo "<h2>1. WordPress Environment Check</h2>";
    echo "<p><strong>WordPress Version:</strong> " . get_bloginfo('version') . "</p>";
    echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
    echo "<p><strong>Site URL:</strong> " . get_site_url() . "</p>";
    echo "<p><strong>Admin Email:</strong> " . get_option('admin_email') . "</p>";
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>2. Active Plugins Check</h2>";
    $active_plugins = get_option('active_plugins');
    $smtp_plugins = [];
    $form_plugins = [];
    
    foreach ($active_plugins as $plugin) {
        if (strpos(strtolower($plugin), 'smtp') !== false || strpos(strtolower($plugin), 'mail') !== false) {
            $smtp_plugins[] = $plugin;
        }
        if (strpos(strtolower($plugin), 'form') !== false || strpos(strtolower($plugin), 'contact') !== false) {
            $form_plugins[] = $plugin;
        }
    }
    
    echo "<p><strong>SMTP/Mail Plugins:</strong></p>";
    if (!empty($smtp_plugins)) {
        echo "<ul>";
        foreach ($smtp_plugins as $plugin) {
            echo "<li>" . $plugin . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='warning'>No SMTP plugins detected</div>";
    }
    
    echo "<p><strong>Form Plugins:</strong></p>";
    if (!empty($form_plugins)) {
        echo "<ul>";
        foreach ($form_plugins as $plugin) {
            echo "<li>" . $plugin . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='info'>No form plugins detected</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>3. PHP Mail Configuration</h2>";
    echo "<p><strong>mail() function:</strong> " . (function_exists('mail') ? '✓ Available' : '✗ Not available') . "</p>";
    echo "<p><strong>sendmail_path:</strong> " . ini_get('sendmail_path') . "</p>";
    echo "<p><strong>SMTP:</strong> " . ini_get('SMTP') . "</p>";
    echo "<p><strong>smtp_port:</strong> " . ini_get('smtp_port') . "</p>";
    echo "</div>";

    // Test wp_mail function
    echo "<div class='section'>";
    echo "<h2>4. WordPress Mail Test</h2>";
    
    if (isset($_POST['test_wp_mail'])) {
        $test_email = sanitize_email($_POST['test_email']);
        $subject = 'WordPress Mail Test - ' . date('Y-m-d H:i:s');
        $message = 'This is a test email from WordPress wp_mail() function.';
        $headers = array('Content-Type: text/html; charset=UTF-8');
        
        // Capture any errors
        $mail_error = '';
        add_action('wp_mail_failed', function($wp_error) use (&$mail_error) {
            $mail_error = $wp_error->get_error_message();
        });
        
        // Enable debug mode
        add_action('phpmailer_init', function($phpmailer) {
            $phpmailer->SMTPDebug = 2;
            $phpmailer->Debugoutput = function($str, $level) {
                echo "<pre>PHPMailer Debug: " . htmlspecialchars($str) . "</pre>";
            };
        });
        
        $result = wp_mail($test_email, $subject, $message, $headers);
        
        if ($result) {
            echo "<div class='success'>✓ wp_mail() returned true - Email should be sent</div>";
        } else {
            echo "<div class='error'>✗ wp_mail() returned false - Email failed to send</div>";
            if ($mail_error) {
                echo "<div class='error'>Error: " . $mail_error . "</div>";
            }
        }
    }
    ?>
    
    <form method="post">
        <p>
            <label>Test Email Address:</label><br>
            <input type="email" name="test_email" value="<EMAIL>" required style="width: 300px;">
        </p>
        <p>
            <input type="submit" name="test_wp_mail" value="Test wp_mail()" style="padding: 10px 20px; background: #0073aa; color: white; border: none;">
        </p>
    </form>
    </div>

    <?php
    echo "<div class='section'>";
    echo "<h2>5. Fluent SMTP Configuration Check</h2>";
    
    // Check if Fluent SMTP is active and configured
    if (is_plugin_active('fluent-smtp/fluent-smtp.php')) {
        echo "<div class='success'>✓ Fluent SMTP plugin is active</div>";
        
        // Try to get Fluent SMTP settings
        $fluent_settings = get_option('fluentmail-settings');
        if ($fluent_settings) {
            echo "<p><strong>Fluent SMTP Settings Found:</strong></p>";
            echo "<pre>" . print_r($fluent_settings, true) . "</pre>";
        } else {
            echo "<div class='warning'>⚠ Fluent SMTP settings not found - plugin may not be configured</div>";
        }
    } else {
        echo "<div class='warning'>⚠ Fluent SMTP plugin is not active</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>6. Forminator Configuration Check</h2>";
    
    if (is_plugin_active('forminator/forminator.php')) {
        echo "<div class='success'>✓ Forminator plugin is active</div>";
        
        // Check Forminator email settings
        $forminator_settings = get_option('forminator_settings');
        if ($forminator_settings) {
            echo "<p><strong>Forminator Settings:</strong></p>";
            echo "<pre>" . print_r($forminator_settings, true) . "</pre>";
        }
    } else {
        echo "<div class='warning'>⚠ Forminator plugin is not active</div>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>7. Recommendations</h2>";
    echo "<div class='info'>";
    echo "<h3>To Fix Email Issues:</h3>";
    echo "<ol>";
    echo "<li><strong>Configure Fluent SMTP:</strong> Go to WordPress Admin → Fluent SMTP → Settings</li>";
    echo "<li><strong>Use these Office 365 settings:</strong>";
    echo "<ul>";
    echo "<li>Host: smtp.office365.com</li>";
    echo "<li>Port: 587</li>";
    echo "<li>Encryption: TLS</li>";
    echo "<li>Username: <EMAIL></li>";
    echo "<li>Password: E\$@24680</li>";
    echo "</ul></li>";
    echo "<li><strong>Test the configuration</strong> in Fluent SMTP settings</li>";
    echo "<li><strong>Check Forminator email settings</strong> to ensure it uses the correct sender email</li>";
    echo "<li><strong>Enable WordPress debug logging</strong> to see detailed error messages</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>8. Quick Fix Code</h2>";
    echo "<p>Add this to your theme's functions.php as a temporary fix:</p>";
    echo "<pre>";
    echo htmlspecialchars("
// Force WordPress to use SMTP for all emails
add_action('phpmailer_init', 'configure_smtp_for_wordpress');
function configure_smtp_for_wordpress(\$phpmailer) {
    \$phpmailer->isSMTP();
    \$phpmailer->Host = 'smtp.office365.com';
    \$phpmailer->SMTPAuth = true;
    \$phpmailer->Username = '<EMAIL>';
    \$phpmailer->Password = 'E\$@24680';
    \$phpmailer->SMTPSecure = 'tls';
    \$phpmailer->Port = 587;
    \$phpmailer->setFrom('<EMAIL>', 'Al Takaful Insurance Services');
}");
    echo "</pre>";
    echo "</div>";
    ?>

    <div class="section">
        <h2>9. Security Note</h2>
        <div class="error">
            <p><strong>Important:</strong> Remove this diagnostic file after testing for security reasons.</p>
            <p>Store email credentials securely using environment variables or WordPress constants.</p>
        </div>
    </div>
</body>
</html>
